import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Video, Mic, MicOff, VideoOff, MessageSquare, Phone, PhoneOff } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { ConsultationSession } from '../../types';

const ConsultationPage = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const [session, setSession] = useState<ConsultationSession | null>(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    // Simulate fetching consultation session
    const mockSession: ConsultationSession = {
      id: id || '1',
      appointmentId: '123',
      patientId: '1',
      doctorId: '2',
      startTime: new Date().toISOString(),
      status: 'active',
      messages: [],
    };
    setSession(mockSession);
  }, [id]);

  const toggleAudio = () => {
    setIsAudioEnabled(!isAudioEnabled);
  };

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
  };

  const toggleConnection = () => {
    setIsConnected(!isConnected);
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    const newMessage = {
      id: Date.now().toString(),
      sender: user?.role === 'doctor' ? 'doctor' : 'user',
      content: message,
      timestamp: new Date().toISOString(),
    };

    setSession(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        messages: [...prev.messages, newMessage],
      };
    });
    setMessage('');
  };

  if (!session) {
    return (
      <div className="min-h-[calc(100vh-140px)] flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading consultation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-140px)] bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Video Area */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-card overflow-hidden">
            <div className="aspect-video bg-gray-900 relative">
              {/* Remote Video (Doctor/Patient) */}
              <div className="absolute inset-0 flex items-center justify-center">
                {isConnected ? (
                  <video className="w-full h-full object-cover\" autoPlay playsInline />
                ) : (
                  <div className="text-center text-white">
                    <Video className="h-16 w-16 mx-auto mb-4 text-gray-500" />
                    <p className="text-lg font-medium">Waiting to connect...</p>
                  </div>
                )}
              </div>

              {/* Local Video Preview */}
              <div className="absolute bottom-4 right-4 w-48 aspect-video bg-gray-800 rounded-lg overflow-hidden">
                <video className="w-full h-full object-cover" autoPlay playsInline muted />
              </div>

              {/* Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-4">
                <button
                  onClick={toggleAudio}
                  className={`p-3 rounded-full ${
                    isAudioEnabled ? 'bg-gray-800 text-white' : 'bg-error-500 text-white'
                  }`}
                >
                  {isAudioEnabled ? <Mic className="h-6 w-6" /> : <MicOff className="h-6 w-6" />}
                </button>

                <button
                  onClick={toggleConnection}
                  className={`p-4 rounded-full ${
                    isConnected ? 'bg-error-500 text-white' : 'bg-success-500 text-white'
                  }`}
                >
                  {isConnected ? <PhoneOff className="h-6 w-6" /> : <Phone className="h-6 w-6" />}
                </button>

                <button
                  onClick={toggleVideo}
                  className={`p-3 rounded-full ${
                    isVideoEnabled ? 'bg-gray-800 text-white' : 'bg-error-500 text-white'
                  }`}
                >
                  {isVideoEnabled ? <Video className="h-6 w-6" /> : <VideoOff className="h-6 w-6" />}
                </button>
              </div>
            </div>

            {/* Session Info */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {user?.role === 'doctor' ? 'Patient Consultation' : 'Doctor Consultation'}
                  </h2>
                  <p className="text-sm text-gray-500">
                    Session ID: {session.id}
                  </p>
                </div>
                <div className="flex items-center">
                  <span className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-success-500' : 'bg-warning-500'
                  }`}></span>
                  <span className="ml-2 text-sm text-gray-600">
                    {isConnected ? 'Connected' : 'Connecting...'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Area */}
          <div className="bg-white rounded-xl shadow-card overflow-hidden flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Consultation Chat</h3>
            </div>

            {/* Messages */}
            <div className="flex-grow p-4 overflow-y-auto space-y-4">
              {session.messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${msg.sender === user?.role ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[75%] rounded-lg px-4 py-2 ${
                    msg.sender === user?.role
                      ? 'bg-primary-100 text-primary-900'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <p className="text-sm">{msg.content}</p>
                    <span className="text-xs text-gray-500 mt-1 block">
                      {new Date(msg.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-grow px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
                <button
                  type="submit"
                  disabled={!message.trim()}
                  className="p-2 bg-primary-500 text-white rounded-full hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <MessageSquare className="h-5 w-5" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationPage;
import { useState } from 'react';
import { FileText, Upload, Download, Eye, Calendar, User, Search, Filter } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface MedicalRecord {
  id: string;
  title: string;
  type: 'lab_result' | 'prescription' | 'diagnosis' | 'imaging' | 'other';
  date: string;
  doctor: string;
  description: string;
  fileUrl?: string;
  fileSize?: string;
}

const MedicalRecordsPage = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null);

  // Mock medical records data
  const medicalRecords: MedicalRecord[] = [
    {
      id: '1',
      title: 'Blood Test Results',
      type: 'lab_result',
      date: '2023-05-20',
      doctor: 'Dr. <PERSON>',
      description: 'Complete blood count and lipid panel results',
      fileUrl: '/files/blood-test-results.pdf',
      fileSize: '2.3 MB',
    },
    {
      id: '2',
      title: 'Hypertension Prescription',
      type: 'prescription',
      date: '2023-05-18',
      doctor: 'Dr. <PERSON>',
      description: 'Prescription for blood pressure medication',
      fileUrl: '/files/prescription-hypertension.pdf',
      fileSize: '1.1 MB',
    },
    {
      id: '3',
      title: 'Chest X-Ray',
      type: 'imaging',
      date: '2023-05-15',
      doctor: 'Dr. Michael Chen',
      description: 'Routine chest X-ray examination',
      fileUrl: '/files/chest-xray.jpg',
      fileSize: '5.7 MB',
    },
    {
      id: '4',
      title: 'Annual Physical Exam',
      type: 'diagnosis',
      date: '2023-05-10',
      doctor: 'Dr. Sarah Johnson',
      description: 'Comprehensive annual health checkup',
      fileUrl: '/files/annual-physical.pdf',
      fileSize: '3.2 MB',
    },
  ];

  const recordTypes = [
    { value: 'all', label: 'All Records' },
    { value: 'lab_result', label: 'Lab Results' },
    { value: 'prescription', label: 'Prescriptions' },
    { value: 'diagnosis', label: 'Diagnoses' },
    { value: 'imaging', label: 'Imaging' },
    { value: 'other', label: 'Other' },
  ];

  const getRecordIcon = (type: string) => {
    switch (type) {
      case 'lab_result':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'prescription':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'diagnosis':
        return <FileText className="h-5 w-5 text-purple-500" />;
      case 'imaging':
        return <FileText className="h-5 w-5 text-orange-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const filteredRecords = medicalRecords.filter(record => {
    const matchesSearch = record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.doctor.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || record.type === selectedType;
    return matchesSearch && matchesType;
  });

  const handleDownload = (record: MedicalRecord) => {
    // Simulate file download
    console.log('Downloading:', record.title);
  };

  const handleView = (record: MedicalRecord) => {
    setSelectedRecord(record);
  };

  return (
    <div className="animate-fade-in">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Medical Records</h1>
        <p className="text-gray-600 mt-1">Access and manage your medical documents</p>
      </div>

      {/* Search and Filter */}
      <div className="bg-white p-6 rounded-xl shadow-card mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search medical records..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {recordTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Upload Section */}
      <div className="bg-white p-6 rounded-xl shadow-card mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Upload New Record</h2>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors cursor-pointer">
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-2">Drag and drop your medical files here, or click to browse</p>
          <p className="text-sm text-gray-500">Supported formats: PDF, JPG, PNG (Max 10MB)</p>
          <button className="mt-4 px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
            Choose Files
          </button>
        </div>
      </div>

      {/* Records List */}
      <div className="bg-white rounded-xl shadow-card">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Your Medical Records ({filteredRecords.length})
          </h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredRecords.map((record) => (
            <div key={record.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getRecordIcon(record.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {record.title}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">{record.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(record.date).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {record.doctor}
                      </div>
                      {record.fileSize && (
                        <span>{record.fileSize}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleView(record)}
                    className="p-2 text-gray-400 hover:text-primary-500 transition-colors"
                    title="View"
                  >
                    <Eye className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleDownload(record)}
                    className="p-2 text-gray-400 hover:text-primary-500 transition-colors"
                    title="Download"
                  >
                    <Download className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredRecords.length === 0 && (
          <div className="p-12 text-center">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No records found</h3>
            <p className="text-gray-500">
              {searchTerm || selectedType !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Upload your first medical record to get started'
              }
            </p>
          </div>
        )}
      </div>

      {/* Record Viewer Modal */}
      {selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">{selectedRecord.title}</h2>
                <button
                  onClick={() => setSelectedRecord(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Type</label>
                  <p className="text-gray-900 capitalize">{selectedRecord.type.replace('_', ' ')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Date</label>
                  <p className="text-gray-900">{new Date(selectedRecord.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Doctor</label>
                  <p className="text-gray-900">{selectedRecord.doctor}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Description</label>
                  <p className="text-gray-900">{selectedRecord.description}</p>
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => handleDownload(selectedRecord)}
                  className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors flex items-center"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </button>
                <button
                  onClick={() => setSelectedRecord(null)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MedicalRecordsPage;

export type UserRole = 'patient' | 'doctor' | 'admin';

export interface User {
  id: string;
  name: string;
  email: string;
  role: User<PERSON><PERSON>;
  avatar?: string;
  specialization?: string; // For doctors
  phone?: string;
  dateCreated: string;
  lastActive?: string;
}

export interface Appointment {
  id: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  date: string;
  time: string;
  duration: number; // in minutes
  status: 'scheduled' | 'completed' | 'cancelled' | 'no-show';
  type: 'video' | 'audio' | 'ai-assisted';
  notes?: string;
}

export interface MedicalRecord {
  id: string;
  patientId: string;
  title: string;
  type: 'lab' | 'imaging' | 'prescription' | 'note' | 'other';
  date: string;
  fileUrl?: string;
  notes?: string;
  doctorId?: string;
  doctorName?: string;
}

export interface Prescription {
  id: string;
  patientId: string;
  doctorId: string;
  doctorName: string;
  date: string;
  medications: Medication[];
  instructions: string;
  status: 'active' | 'completed' | 'cancelled';
}

export interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'appointment' | 'message' | 'prescription' | 'system';
  read: boolean;
  date: string;
  link?: string;
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai' | 'doctor';
  content: string;
  timestamp: string;
  attachments?: string[];
}

export interface ConsultationSession {
  id: string;
  appointmentId: string;
  patientId: string;
  doctorId: string;
  startTime: string;
  endTime?: string;
  status: 'waiting' | 'active' | 'completed' | 'ai-mode';
  notes?: string;
  messages: ChatMessage[];
}
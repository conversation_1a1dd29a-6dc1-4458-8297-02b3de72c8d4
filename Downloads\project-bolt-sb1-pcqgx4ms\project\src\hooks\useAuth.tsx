import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserRole } from '../types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string, role: UserRole) => Promise<boolean>;
  logout: () => void;
}

// Mock user data
const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'patient',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    dateCreated: '2023-01-15',
    lastActive: '2023-05-22',
    phone: '+****************',
  },
  {
    id: '2',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'doctor',
    avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
    specialization: 'Cardiology',
    dateCreated: '2022-11-05',
    lastActive: '2023-05-23',
    phone: '+****************',
  },
  {
    id: '3',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    dateCreated: '2022-09-01',
    lastActive: '2023-05-22',
    phone: '+****************',
  }
];

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in from localStorage
    const storedUser = localStorage.getItem('telemed_user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    // Simulate API call
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const foundUser = mockUsers.find(u => u.email === email);
    if (foundUser && password === 'password') { // Simple password check for demo
      setUser(foundUser);
      localStorage.setItem('telemed_user', JSON.stringify(foundUser));
      setLoading(false);
      return true;
    }
    
    setLoading(false);
    return false;
  };

  const register = async (
    name: string, 
    email: string, 
    password: string, 
    role: UserRole
  ): Promise<boolean> => {
    // Simulate API call
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const existingUser = mockUsers.find(u => u.email === email);
    if (existingUser) {
      setLoading(false);
      return false;
    }
    
    const newUser: User = {
      id: `${mockUsers.length + 1}`,
      name,
      email,
      role,
      dateCreated: new Date().toISOString(),
    };
    
    // In a real app, we would call an API to create the user
    // For demo purposes, we'll just set the user state
    setUser(newUser);
    localStorage.setItem('telemed_user', JSON.stringify(newUser));
    setLoading(false);
    return true;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('telemed_user');
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        loading,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
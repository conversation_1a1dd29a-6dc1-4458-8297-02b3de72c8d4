import { useState, useRef, useEffect } from 'react';
import { Bo<PERSON>, Send, Paperclip, Mic, Image, X } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { ChatMessage } from '../../types';

const ChatbotPage = () => {
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      sender: 'ai',
      content: `Hello ${user?.name}, I'm your AI health assistant. How can I help you today?`,
      timestamp: new Date().toISOString(),
    }
  ]);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) return;
    
    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'user',
      content: message,
      timestamp: new Date().toISOString(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsTyping(true);
    
    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponse = generateAIResponse(message);
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  // Simple AI response generator for demo purposes
  const generateAIResponse = (userMessage: string): ChatMessage => {
    const lowerCaseMessage = userMessage.toLowerCase();
    let response = '';
    
    if (lowerCaseMessage.includes('headache')) {
      response = "I'm sorry to hear you're experiencing headaches. Could you tell me more about the frequency, intensity, and any associated symptoms? This will help me better understand your situation.";
    } else if (lowerCaseMessage.includes('cold') || lowerCaseMessage.includes('flu')) {
      response = "It sounds like you might be experiencing cold or flu symptoms. Common treatments include rest, staying hydrated, and over-the-counter medications for symptom relief. If symptoms are severe or persist for more than a week, I recommend consulting with a doctor.";
    } else if (lowerCaseMessage.includes('sleep') || lowerCaseMessage.includes('insomnia')) {
      response = "Sleep issues can significantly impact your health. Consider establishing a regular sleep schedule, creating a comfortable sleep environment, and avoiding screens before bed. If insomnia persists, it might be worth discussing with a healthcare provider.";
    } else if (lowerCaseMessage.includes('anxiety') || lowerCaseMessage.includes('stress')) {
      response = "Managing anxiety and stress is important for your overall well-being. Deep breathing exercises, regular physical activity, and mindfulness practices can be helpful. Would you like me to suggest some specific techniques you could try?";
    } else if (lowerCaseMessage.includes('appointment')) {
      response = "I'd be happy to help you schedule an appointment. You can visit the Appointments section to book a consultation with a healthcare provider of your choice.";
    } else {
      response = "Thank you for sharing that information. Is there anything specific about your health concerns that you'd like to discuss further? I'm here to provide guidance based on your needs.";
    }
    
    return {
      id: Date.now().toString(),
      sender: 'ai',
      content: response,
      timestamp: new Date().toISOString(),
    };
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="h-[calc(100vh-140px)] flex flex-col bg-white rounded-xl shadow-card overflow-hidden animate-fade-in">
      <div className="bg-accent-100 p-4 flex items-center border-b border-accent-200">
        <div className="w-10 h-10 rounded-full bg-accent-200 flex items-center justify-center mr-3">
          <Bot className="h-5 w-5 text-accent-600" />
        </div>
        <div>
          <h1 className="text-lg font-semibold text-gray-900">AI Health Assistant</h1>
          <p className="text-sm text-gray-600">24/7 healthcare guidance</p>
        </div>
      </div>
      
      <div className="flex-grow overflow-y-auto p-4 bg-gray-50">
        <div className="space-y-4">
          {messages.map((msg) => (
            <div 
              key={msg.id} 
              className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {msg.sender === 'ai' && (
                <div className="w-8 h-8 rounded-full bg-accent-100 flex items-center justify-center mr-2 flex-shrink-0">
                  <Bot className="h-4 w-4 text-accent-600" />
                </div>
              )}
              
              <div className={`max-w-[75%] rounded-lg px-4 py-3 ${
                msg.sender === 'user' 
                  ? 'bg-primary-100 text-primary-900' 
                  : 'bg-white border border-gray-200 text-gray-800'
              }`}>
                <p className="text-sm">{msg.content}</p>
                <div className={`text-xs mt-1 ${
                  msg.sender === 'user' ? 'text-primary-600' : 'text-gray-500'
                }`}>
                  {formatTime(msg.timestamp)}
                </div>
              </div>
              
              {msg.sender === 'user' && (
                <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center ml-2 flex-shrink-0">
                  <span className="text-primary-600 text-xs font-medium">
                    {user?.name?.charAt(0)}
                  </span>
                </div>
              )}
            </div>
          ))}
          
          {isTyping && (
            <div className="flex justify-start">
              <div className="w-8 h-8 rounded-full bg-accent-100 flex items-center justify-center mr-2 flex-shrink-0">
                <Bot className="h-4 w-4 text-accent-600" />
              </div>
              <div className="bg-white border border-gray-200 rounded-lg px-4 py-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-accent-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-accent-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-accent-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center">
          <button 
            type="button" 
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Paperclip className="h-5 w-5" />
          </button>
          <button 
            type="button" 
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Mic className="h-5 w-5" />
          </button>
          <button 
            type="button" 
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Image className="h-5 w-5" />
          </button>
          
          <div className="flex-grow mx-2 relative">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your health question..."
              className="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
            />
            {message && (
              <button 
                type="button" 
                onClick={() => setMessage('')} 
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          
          <button 
            type="submit" 
            disabled={!message.trim() || isTyping}
            className="p-2 bg-accent-500 text-white rounded-full hover:bg-accent-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-5 w-5" />
          </button>
        </div>
        
        <div className="mt-2 text-xs text-gray-500 text-center">
          <p>
            The AI assistant provides general health information and is not a substitute for professional medical advice.
          </p>
        </div>
      </form>
    </div>
  );
};

export default ChatbotPage;